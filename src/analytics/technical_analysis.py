# Technical analysis indicators harvested from soltrade
# To be refactored and integrated into the NEXUS analytics layer

import pandas as pd
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from src.data.models import SignalModel

logger = logging.getLogger(__name__)

def calculate_ema(dataframe: pd.DataFrame, length: int) -> int:
    ema = dataframe['close'].ewm(span=length, adjust=False).mean()
    return ema.iat[-1]

def calculate_bbands(dataframe: pd.DataFrame, length: int) -> pd.Series:
    sma = dataframe['close'].rolling(length).mean()
    std = dataframe['close'].rolling(length).std()
    upper_bband = sma + std * 2
    lower_bband = sma - std * 2
    return upper_bband, lower_bband

def calculate_rsi(dataframe: pd.DataFrame, length: int) -> int:
    delta = dataframe['close'].diff()
    up = delta.clip(lower=0)
    down = delta.clip(upper=0).abs()
    upper_ema = up.ewm(com=length - 1, adjust=False, min_periods=length).mean()
    lower_ema = down.ewm(com=length - 1, adjust=False, min_periods=length).mean()
    rsi = upper_ema / lower_ema
    rsi = 100 - (100 / (1 + rsi))
    return rsi.iat[-1]


def generate_technical_signal(market_data) -> Optional[SignalModel]:
    """
    Generate technical analysis signal from market data.

    This function implements basic momentum and technical analysis logic:
    - Momentum signals based on price movement
    - RSI overbought/oversold conditions
    - EMA crossover signals

    Args:
        market_data: Market data object or dict containing price information

    Returns:
        SignalModel object if signal conditions are met, None otherwise
    """
    try:
        # Extract price data from market_data
        if hasattr(market_data, 'price_usd'):
            current_price = market_data.price_usd
            token_address = getattr(market_data, 'token_address', 'unknown')
        elif isinstance(market_data, dict):
            current_price = market_data.get('price_usd', market_data.get('price', 0))
            token_address = market_data.get('token_address', market_data.get('token', 'unknown'))
        else:
            logger.warning("Invalid market_data format for technical analysis")
            return None

        if not current_price or current_price <= 0:
            logger.warning("Invalid price data for technical analysis")
            return None

        # For now, implement a simple momentum-based signal
        # This will be enhanced with proper historical data analysis

        # Basic momentum signal logic (placeholder)
        # In a real implementation, this would use historical price data
        confidence = 0.5  # Base confidence
        direction = None

        # Simple price-based heuristics (to be replaced with proper TA)
        if current_price > 0:
            # Generate buy signal for demonstration
            # This is a placeholder - real implementation would use historical data
            direction = "BUY"
            confidence = 0.6

        if direction:
            return SignalModel(
                token=token_address,
                entry_price=current_price,
                direction=direction,
                confidence=confidence,
                timestamp=datetime.now(),
                source="technical_analysis"
            )

        return None

    except Exception as e:
        logger.error(f"Error generating technical signal: {e}")
        return None
