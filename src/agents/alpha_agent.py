"""
ALPHA Agent: Market Intelligence and Signal Generation
Consolidates analytics and intelligence to produce actionable trading signals.
Enhanced with ML prediction capabilities from rife-ai backend.
"""
import asyncio
import logging
from typing import Optional, Dict, List, Any
from datetime import datetime

from src.data.models import SignalModel
from src.analytics.technical_analysis import generate_technical_signal
# from src.analytics.whale_tracking import detect_whale_activity  # COMMENTED OUT: TypeScript module, not importable in Python
from src.analytics.rug_detector import RugDetector
from src.core.monitoring.monitoring import log_event, health_check
from src.ml.prediction_system import RealTimePredictionSystem, PredictionResult
from src.data.phase1_data_pipeline_unification import UnifiedDataService
from src.core.client import SolanaClient

class AlphaAgent:
    def __init__(self,
                 rug_detector: Optional[RugDetector] = None,
                 data_service: Optional[UnifiedDataService] = None,
                 solana_client: Optional[SolanaClient] = None,
                 enable_ml: bool = True):
        self.rug_detector = rug_detector
        self.data_service = data_service or UnifiedDataService()
        self.solana_client = solana_client
        self.enable_ml = enable_ml
        self.ml_system: Optional[RealTimePredictionSystem] = None
        self.is_initialized = False

        # SOL token address for momentum signals
        self.sol_mint = "So11111111111111111111111111111111111111112"

        log_event("AlphaAgent", "Initialized AlphaAgent with ML capabilities")

    async def initialize(self):
        """Initialize the ALPHA agent with ML capabilities"""
        try:
            if self.enable_ml and self.data_service:
                self.ml_system = RealTimePredictionSystem()
                await self.ml_system.initialize(self.data_service)
                log_event("AlphaAgent", "ML prediction system initialized")

            self.is_initialized = True
            log_event("AlphaAgent", "ALPHA agent initialization complete")

        except Exception as e:
            logging.error(f"Failed to initialize ALPHA agent: {e}")
            self.is_initialized = False

    async def generate_signal(self, market_data, token: Optional[str] = None) -> Optional[SignalModel]:
        """
        Generate a trading signal using analytics modules, ML predictions, and token safety check.
        Enhanced with ML capabilities from rife-ai backend.
        Returns a SignalModel or None if no actionable signal.
        """
        log_event("AlphaAgent", "Generating enhanced signal with ML")

        # Extract token from market_data if not provided
        if not token:
            token = getattr(market_data, 'token', None) or market_data.get('token') if isinstance(market_data, dict) else None

        if not token:
            log_event("AlphaAgent", "No token specified for signal generation", level="warning")
            return None

        # Generate traditional technical analysis signal
        ta_signal = generate_technical_signal(market_data)

        # Generate ML prediction if available
        ml_signal = None
        if self.ml_system and self.is_initialized:
            try:
                ml_prediction = await self.ml_system.get_prediction(token)
                if ml_prediction:
                    ml_signal = self.ml_system.to_signal_model(ml_prediction)
                    log_event("AlphaAgent", f"ML prediction generated for {token}: {ml_prediction.direction}")
            except Exception as e:
                logging.error(f"Error getting ML prediction: {e}")

        # Check token safety if rug detector is available
        if self.rug_detector and token:
            try:
                safety = await self.rug_detector.analyze_token_safety(token)
                if not safety.is_safe:
                    log_event("AlphaAgent", f"Unsafe token detected: {token}", level="warning")
                    return None  # Do not trade unsafe tokens
            except Exception as e:
                logging.error(f"Error checking token safety: {e}")

        # Combine signals using weighted approach
        final_signal = self._combine_signals(ta_signal, ml_signal, token)

        if final_signal:
            log_event("AlphaAgent", f"Generated signal for {token}: {final_signal.direction} (confidence: {final_signal.confidence:.2f})")
        else:
            log_event("AlphaAgent", f"No actionable signal generated for {token}", level="info")

        return final_signal

    async def process(self, token: Optional[str] = None) -> Optional[SignalModel]:
        """
        Main processing method that fetches real-time data and generates signals.
        This is the primary entry point for the ALPHA Agent.

        Args:
            token: Token address to analyze (defaults to SOL)

        Returns:
            SignalModel if actionable signal is generated, None otherwise
        """
        if not self.is_initialized:
            await self.initialize()

        # Default to SOL if no token specified
        target_token = token or self.sol_mint

        try:
            # Fetch real-time market data
            market_data = await self._fetch_market_data(target_token)
            if not market_data:
                log_event("AlphaAgent", f"No market data available for {target_token[:8]}...", level="warning")
                return None

            # Generate momentum signal
            momentum_signal = await self._generate_momentum_signal(market_data, target_token)
            if momentum_signal:
                log_event("AlphaAgent", f"Momentum signal generated: {momentum_signal.direction} (confidence: {momentum_signal.confidence:.2f})")
                return momentum_signal

            # Fallback to existing signal generation
            return await self.generate_signal(market_data, target_token)

        except Exception as e:
            logging.error(f"Error in ALPHA Agent processing: {e}")
            return None

    async def _fetch_market_data(self, token_address: str) -> Optional[Dict[str, Any]]:
        """Fetch real-time market data for the specified token"""
        try:
            if self.data_service:
                # Use UnifiedDataService to get price data
                price_data = await self.data_service.get_price(token_address)
                if price_data:
                    return {
                        'token_address': token_address,
                        'price_usd': price_data.price_usd,
                        'volume_24h': price_data.volume_24h,
                        'last_updated': price_data.last_updated,
                        'source': price_data.source
                    }

            log_event("AlphaAgent", f"Failed to fetch market data for {token_address[:8]}...", level="warning")
            return None

        except Exception as e:
            logging.error(f"Error fetching market data: {e}")
            return None

    async def _generate_momentum_signal(self, market_data: Dict[str, Any], token: str) -> Optional[SignalModel]:
        """
        Generate momentum-based trading signal.

        Momentum logic: If price increased >2% in recent period, generate BUY signal.
        Uses historical price data when available, falls back to heuristics.
        """
        try:
            current_price = market_data.get('price_usd', 0)
            if not current_price or current_price <= 0:
                return None

            # Try to get historical price data for momentum calculation
            historical_price = await self._get_historical_price(token, hours_ago=1)

            if historical_price and historical_price > 0:
                # Calculate actual momentum percentage
                price_change_pct = ((current_price - historical_price) / historical_price) * 100

                log_event("AlphaAgent", f"Price momentum: {price_change_pct:.2f}% (${historical_price:.2f} -> ${current_price:.2f})")

                # Generate BUY signal if price increased >2%
                if price_change_pct > 2.0:
                    # Scale confidence based on momentum strength
                    confidence = min(0.9, 0.5 + (price_change_pct / 20))  # Higher momentum = higher confidence

                    return SignalModel(
                        token=token,
                        entry_price=current_price,
                        direction="BUY",
                        confidence=confidence,
                        timestamp=datetime.now(),
                        source="alpha_momentum"
                    )

                # Generate SELL signal if price decreased >3% (stronger threshold for sells)
                elif price_change_pct < -3.0:
                    confidence = min(0.8, 0.4 + (abs(price_change_pct) / 25))

                    return SignalModel(
                        token=token,
                        entry_price=current_price,
                        direction="SELL",
                        confidence=confidence,
                        timestamp=datetime.now(),
                        source="alpha_momentum"
                    )
            else:
                # Fallback heuristic when no historical data available
                if token == self.sol_mint and current_price > 0:
                    # Simple price-level based signal for demonstration
                    if current_price > 150:  # If SOL > $150, consider bullish
                        confidence = min(0.6, current_price / 300)

                        return SignalModel(
                            token=token,
                            entry_price=current_price,
                            direction="BUY",
                            confidence=confidence,
                            timestamp=datetime.now(),
                            source="alpha_momentum_heuristic"
                        )

            return None

        except Exception as e:
            logging.error(f"Error generating momentum signal: {e}")
            return None

    async def _get_historical_price(self, token: str, hours_ago: int = 1) -> Optional[float]:
        """
        Get historical price data for momentum calculation.

        Args:
            token: Token address
            hours_ago: How many hours back to look

        Returns:
            Historical price in USD, or None if not available
        """
        try:
            # For now, simulate historical price by applying a random variation
            # In production, this would query actual historical data from the data service

            current_data = await self.data_service.get_price(token) if self.data_service else None
            if current_data and current_data.price_usd:
                # Simulate historical price with 5% random variation for demonstration
                import random
                variation = random.uniform(-0.05, 0.05)  # ±5% variation
                historical_price = current_data.price_usd * (1 + variation)

                log_event("AlphaAgent", f"Simulated historical price: ${historical_price:.2f} ({hours_ago}h ago)")
                return historical_price

            return None

        except Exception as e:
            logging.error(f"Error getting historical price: {e}")
            return None

    def _combine_signals(self, ta_signal: Optional[SignalModel], ml_signal: Optional[SignalModel], token: str) -> Optional[SignalModel]:
        """
        Combine technical analysis and ML signals using weighted approach
        """
        try:
            # If no signals available, return None
            if not ta_signal and not ml_signal:
                return None

            # If only one signal available, return it (with adjusted confidence)
            if ta_signal and not ml_signal:
                ta_signal.confidence *= 0.8  # Reduce confidence when no ML confirmation
                return ta_signal

            if ml_signal and not ta_signal:
                ml_signal.confidence *= 0.7  # ML alone gets lower weight
                return ml_signal

            # Both signals available - combine them
            # Weight: TA = 0.4, ML = 0.6 (ML gets higher weight)
            ta_weight = 0.4
            ml_weight = 0.6

            # Check if signals agree on direction
            if ta_signal.direction == ml_signal.direction:
                # Signals agree - boost confidence
                combined_confidence = (ta_signal.confidence * ta_weight + ml_signal.confidence * ml_weight) * 1.2
                combined_confidence = min(combined_confidence, 1.0)  # Cap at 1.0

                return SignalModel(
                    token=token,
                    entry_price=ta_signal.entry_price,  # Use TA price as base
                    direction=ta_signal.direction,
                    confidence=combined_confidence,
                    timestamp=datetime.now(),
                    source="combined_ta_ml"
                )
            else:
                # Signals disagree - reduce confidence or return None if too low
                combined_confidence = (ta_signal.confidence * ta_weight + ml_signal.confidence * ml_weight) * 0.6

                if combined_confidence < 0.3:
                    return None  # Too uncertain

                # Use the signal with higher confidence
                if ta_signal.confidence > ml_signal.confidence:
                    ta_signal.confidence = combined_confidence
                    ta_signal.source = "ta_dominant_combined"
                    return ta_signal
                else:
                    ml_signal.confidence = combined_confidence
                    ml_signal.source = "ml_dominant_combined"
                    return ml_signal

        except Exception as e:
            logging.error(f"Error combining signals: {e}")
            return ta_signal or ml_signal  # Return any available signal as fallback

    def health(self) -> bool:
        """Enhanced health check for AlphaAgent with ML capabilities."""
        try:
            base_health = health_check("AlphaAgent", lambda: True)

            # Check ML system health if enabled
            if self.enable_ml and self.ml_system:
                ml_health = self.ml_system.get_system_health()
                ml_ok = ml_health.get('initialized', False)
                log_event("AlphaAgent", f"ML system health: {ml_ok}")
                return base_health and ml_ok

            return base_health

        except Exception as e:
            logging.error(f"Error in health check: {e}")
            return False

    def get_system_status(self) -> Dict[str, Any]:
        """Get detailed system status"""
        status = {
            'initialized': self.is_initialized,
            'rug_detector_available': self.rug_detector is not None,
            'data_service_available': self.data_service is not None,
            'ml_enabled': self.enable_ml,
            'ml_system_available': self.ml_system is not None
        }

        if self.ml_system:
            status['ml_system_health'] = self.ml_system.get_system_health()

        return status
