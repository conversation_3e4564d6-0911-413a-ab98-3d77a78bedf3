#!/usr/bin/env python3
"""
Database Models for 20 PIP Challenge Trading System
SQLAlchemy models for PostgreSQL database integration

Features:
- Trade history tracking
- Portfolio management
- Performance analytics
- Token tracking
- Risk management data
"""

from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Boolean, Text, BigInteger
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
from datetime import datetime
import uuid
import logging
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any

logger = logging.getLogger(__name__)

Base = declarative_base()


class Trade(Base):
    """Trade execution records"""
    __tablename__ = 'trades'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    transaction_signature = Column(String(100), unique=True, nullable=False)
    wallet_address = Column(String(50), nullable=False)
    
    # Trade details
    action = Column(String(10), nullable=False)  # 'buy' or 'sell'
    token_address = Column(String(50), nullable=False)
    token_symbol = Column(String(20))
    
    # Amounts
    input_mint = Column(String(50), nullable=False)
    output_mint = Column(String(50), nullable=False)
    input_amount = Column(BigInteger, nullable=False)
    output_amount = Column(BigInteger, nullable=False)
    
    # Execution details
    dex_used = Column(String(20), nullable=False)
    slippage_bps = Column(Integer, default=100)
    price_impact = Column(Float, default=0.0)
    
    # Timing
    executed_at = Column(DateTime, default=datetime.utcnow)
    execution_time_ms = Column(Integer, default=0)
    
    # Financial
    sol_amount = Column(Float, nullable=False)
    usd_value = Column(Float, default=0.0)
    fees_paid = Column(Float, default=0.0)
    
    # MEV Protection
    mev_protected = Column(Boolean, default=False)
    bundle_id = Column(String(100))
    
    # Metadata
    extra_data = Column(JSONB)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class Portfolio(Base):
    """Portfolio holdings and balances"""
    __tablename__ = 'portfolio'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    wallet_address = Column(String(50), nullable=False)
    
    # Token details
    token_address = Column(String(50), nullable=False)
    token_symbol = Column(String(20))
    token_name = Column(String(100))
    
    # Holdings
    balance = Column(BigInteger, default=0)
    average_buy_price = Column(Float, default=0.0)
    total_invested = Column(Float, default=0.0)
    
    # Performance
    current_value = Column(Float, default=0.0)
    unrealized_pnl = Column(Float, default=0.0)
    realized_pnl = Column(Float, default=0.0)
    
    # Tracking
    first_purchase = Column(DateTime)
    last_transaction = Column(DateTime)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class TradingSession(Base):
    """Trading session tracking"""
    __tablename__ = 'trading_sessions'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    wallet_address = Column(String(50), nullable=False)
    
    # Session details
    session_name = Column(String(100), default="20 PIP Challenge")
    strategy_used = Column(String(50))
    
    # Capital
    starting_capital = Column(Float, nullable=False)
    current_capital = Column(Float, nullable=False)
    peak_capital = Column(Float, default=0.0)
    
    # Performance
    total_trades = Column(Integer, default=0)
    winning_trades = Column(Integer, default=0)
    losing_trades = Column(Integer, default=0)
    win_rate = Column(Float, default=0.0)
    
    # PnL
    total_pnl = Column(Float, default=0.0)
    daily_pnl = Column(Float, default=0.0)
    max_drawdown = Column(Float, default=0.0)
    
    # Timing
    started_at = Column(DateTime, default=datetime.utcnow)
    last_trade_at = Column(DateTime)
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Metadata
    extra_data = Column(JSONB)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class TokenTracking(Base):
    """Token tracking and analysis"""
    __tablename__ = 'token_tracking'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    token_address = Column(String(50), unique=True, nullable=False)
    
    # Token info
    symbol = Column(String(20))
    name = Column(String(100))
    decimals = Column(Integer, default=9)
    
    # Market data
    current_price_sol = Column(Float, default=0.0)
    current_price_usd = Column(Float, default=0.0)
    market_cap = Column(Float, default=0.0)
    volume_24h = Column(Float, default=0.0)
    
    # Trading data
    total_trades = Column(Integer, default=0)
    total_volume = Column(Float, default=0.0)
    average_trade_size = Column(Float, default=0.0)
    
    # Risk assessment
    rug_score = Column(Float, default=0.0)
    liquidity_score = Column(Float, default=0.0)
    volatility_score = Column(Float, default=0.0)
    
    # Flags
    is_pump_token = Column(Boolean, default=False)
    is_migrated = Column(Boolean, default=False)
    is_blacklisted = Column(Boolean, default=False)
    
    # Tracking
    first_seen = Column(DateTime, default=datetime.utcnow)
    last_updated = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Metadata
    extra_data = Column(JSONB)


class PriceHistory(Base):
    """Historical price data"""
    __tablename__ = 'price_history'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    token_address = Column(String(50), nullable=False)
    
    # Price data
    price_sol = Column(Float, nullable=False)
    price_usd = Column(Float, default=0.0)
    volume = Column(Float, default=0.0)
    market_cap = Column(Float, default=0.0)
    
    # Source
    source = Column(String(20), default='jupiter')
    
    # Timing
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # Metadata
    extra_data = Column(JSONB)


class RiskEvent(Base):
    """Risk management events"""
    __tablename__ = 'risk_events'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    wallet_address = Column(String(50), nullable=False)
    
    # Event details
    event_type = Column(String(50), nullable=False)  # 'stop_loss', 'take_profit', 'rug_detected', etc.
    severity = Column(String(20), default='medium')  # 'low', 'medium', 'high', 'critical'
    
    # Related data
    token_address = Column(String(50))
    trade_id = Column(UUID(as_uuid=True))
    
    # Risk metrics
    risk_score = Column(Float, default=0.0)
    confidence = Column(Float, default=0.0)
    
    # Action taken
    action_taken = Column(String(100))
    prevented_loss = Column(Float, default=0.0)
    
    # Details
    description = Column(Text)
    
    # Timing
    detected_at = Column(DateTime, default=datetime.utcnow)
    resolved_at = Column(DateTime)
    
    # Metadata
    extra_data = Column(JSONB)


class PerformanceMetrics(Base):
    """Daily performance metrics"""
    __tablename__ = 'performance_metrics'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    wallet_address = Column(String(50), nullable=False)
    date = Column(DateTime, nullable=False)
    
    # Daily metrics
    starting_balance = Column(Float, nullable=False)
    ending_balance = Column(Float, nullable=False)
    daily_pnl = Column(Float, default=0.0)
    daily_return_pct = Column(Float, default=0.0)
    
    # Trading activity
    trades_count = Column(Integer, default=0)
    winning_trades = Column(Integer, default=0)
    losing_trades = Column(Integer, default=0)
    total_volume = Column(Float, default=0.0)
    
    # Risk metrics
    max_drawdown = Column(Float, default=0.0)
    volatility = Column(Float, default=0.0)
    sharpe_ratio = Column(Float, default=0.0)
    
    # Fees and costs
    total_fees = Column(Float, default=0.0)
    gas_costs = Column(Float, default=0.0)
    
    # Metadata
    extra_data = Column(JSONB)
    created_at = Column(DateTime, default=datetime.utcnow)


class DatabaseManager:
    """Database connection and session management"""
    
    def __init__(self, database_url: str = None):
        # Default to local PostgreSQL if no URL provided
        self.database_url = database_url or "postgresql://trading_user:trading_password@localhost:5432/trading_db"
        
        try:
            self.engine = create_engine(self.database_url, echo=False)
            self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
            
            # Create tables if they don't exist
            Base.metadata.create_all(bind=self.engine)
            
            logger.info("🗄️ Database connection established")
            
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise
    
    def get_session(self):
        """Get database session"""
        return self.SessionLocal()
    
    def close(self):
        """Close database connection"""
        if hasattr(self, 'engine'):
            self.engine.dispose()


# Global database manager
_db_manager: Optional[DatabaseManager] = None


def get_database_manager() -> DatabaseManager:
    """Get global database manager instance"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
    return _db_manager


def get_db_session():
    """Get database session"""
    db_manager = get_database_manager()
    return db_manager.get_session()


# Pydantic models for data validation
class TradeModel(BaseModel):
    id: Optional[str]
    transaction_signature: str
    wallet_address: str
    action: str
    token_address: str
    token_symbol: Optional[str]
    input_mint: str
    output_mint: str
    input_amount: int
    output_amount: int
    dex_used: str
    slippage_bps: Optional[int] = 100
    price_impact: Optional[float] = 0.0
    executed_at: Optional[datetime]
    execution_time_ms: Optional[int] = 0
    sol_amount: float
    usd_value: Optional[float] = 0.0
    fees_paid: Optional[float] = 0.0
    mev_protected: Optional[bool] = False
    bundle_id: Optional[str]
    extra_data: Optional[Dict[str, Any]]
    created_at: Optional[datetime]
    updated_at: Optional[datetime]

class PortfolioModel(BaseModel):
    id: Optional[str]
    wallet_address: str
    token_address: str
    token_symbol: Optional[str]
    token_name: Optional[str]
    balance: Optional[int] = 0
    average_buy_price: Optional[float] = 0.0
    total_invested: Optional[float] = 0.0
    current_value: Optional[float] = 0.0
    unrealized_pnl: Optional[float] = 0.0
    realized_pnl: Optional[float] = 0.0
    first_purchase: Optional[datetime]
    last_transaction: Optional[datetime]
    created_at: Optional[datetime]
    updated_at: Optional[datetime]

class TradingSessionModel(BaseModel):
    id: Optional[str]
    wallet_address: str
    session_name: Optional[str] = "20 PIP Challenge"
    strategy_used: Optional[str]
    starting_capital: float
    current_capital: float
    peak_capital: Optional[float] = 0.0
    total_trades: Optional[int] = 0
    winning_trades: Optional[int] = 0
    losing_trades: Optional[int] = 0
    win_rate: Optional[float] = 0.0
    total_pnl: Optional[float] = 0.0
    daily_pnl: Optional[float] = 0.0
    max_drawdown: Optional[float] = 0.0
    started_at: Optional[datetime]
    last_trade_at: Optional[datetime]
    is_active: Optional[bool] = True
    extra_data: Optional[Dict[str, Any]]
    created_at: Optional[datetime]
    updated_at: Optional[datetime]

class TokenTrackingModel(BaseModel):
    id: Optional[str]
    token_address: str
    symbol: Optional[str]
    name: Optional[str]
    decimals: Optional[int] = 9
    current_price_sol: Optional[float] = 0.0
    current_price_usd: Optional[float] = 0.0
    market_cap: Optional[float] = 0.0
    volume_24h: Optional[float] = 0.0
    total_trades: Optional[int] = 0
    total_volume: Optional[float] = 0.0
    average_trade_size: Optional[float] = 0.0
    rug_score: Optional[float] = 0.0
    liquidity_score: Optional[float] = 0.0
    volatility_score: Optional[float] = 0.0
    is_pump_token: Optional[bool] = False
    is_migrated: Optional[bool] = False
    is_blacklisted: Optional[bool] = False
    first_seen: Optional[datetime]
    last_updated: Optional[datetime]
    extra_data: Optional[Dict[str, Any]]

class PriceHistoryModel(BaseModel):
    id: Optional[str]
    token_address: str
    price_sol: float
    price_usd: Optional[float] = 0.0
    volume: Optional[float] = 0.0
    market_cap: Optional[float] = 0.0
    source: Optional[str] = 'jupiter'
    timestamp: Optional[datetime]
    extra_data: Optional[Dict[str, Any]]

class RiskEventModel(BaseModel):
    id: Optional[str]
    wallet_address: str
    event_type: str
    severity: Optional[str] = 'medium'
    token_address: Optional[str]
    trade_id: Optional[str]
    risk_score: Optional[float] = 0.0
    confidence: Optional[float] = 0.0
    action_taken: Optional[str]
    prevented_loss: Optional[float] = 0.0
    description: Optional[str]
    detected_at: Optional[datetime]
    resolved_at: Optional[datetime]
    extra_data: Optional[Dict[str, Any]]

class PerformanceMetricsModel(BaseModel):
    id: Optional[str]
    wallet_address: str
    date: datetime
    starting_balance: float
    ending_balance: float
    daily_pnl: Optional[float] = 0.0
    daily_return_pct: Optional[float] = 0.0
    trades_count: Optional[int] = 0
    winning_trades: Optional[int] = 0
    losing_trades: Optional[int] = 0
    total_volume: Optional[float] = 0.0
    max_drawdown: Optional[float] = 0.0
    volatility: Optional[float] = 0.0
    sharpe_ratio: Optional[float] = 0.0
    total_fees: Optional[float] = 0.0
    gas_costs: Optional[float] = 0.0
    extra_data: Optional[Dict[str, Any]]
    created_at: Optional[datetime]

class SignalModel(BaseModel):
    token: str
    entry_price: float
    direction: str  # 'BUY', 'SELL', 'HOLD'
    confidence: float  # 0.0 to 1.0
    timestamp: Optional[datetime] = None
    source: Optional[str] = None

    def __init__(self, **data):
        if 'timestamp' not in data or data['timestamp'] is None:
            data['timestamp'] = datetime.now()
        super().__init__(**data)

class TradeOrderModel(BaseModel):
    token: str
    entry_price: float
    direction: str
    confidence: float
    position_size: float
    stop_loss: float
    take_profit: float
    # Add more fields as needed for execution


if __name__ == "__main__":
    # Test database connection
    try:
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # Test query
        trade_count = session.query(Trade).count()
        logger.info(f"Database connected successfully. Trade count: {trade_count}")
        
        session.close()
        db_manager.close()
        
    except Exception as e:
        logger.error(f"Database test failed: {e}")
