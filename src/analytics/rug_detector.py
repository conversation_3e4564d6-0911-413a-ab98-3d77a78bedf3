"""
🚀 20 PIP Challenge - Rug Pull Detection System

Advanced rug-pull detection for Solana meme coins
Protects against common scam patterns and suspicious tokens
"""

import logging
import asyncio
import os
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
import requests
from solana.rpc.async_api import AsyncClient
from solders.pubkey import Pubkey

@dataclass
class TokenSafety:
    """Token safety analysis result"""
    token_address: str
    symbol: str
    is_safe: bool
    risk_score: int  # 0-100, higher = more risky
    warnings: List[str]
    checks: Dict[str, bool]
    metadata: Dict[str, Any]

class RugDetector:
    """
    Advanced rug-pull detection system for Solana tokens
    
    Checks:
    - Mint authority disabled
    - Freeze authority disabled
    - Liquidity locked/burned
    - Creator wallet patterns
    - Token distribution
    - Social signals
    - Historical patterns
    """
    
    def __init__(self, rpc_client: AsyncClient, birdeye_api_key: str):
        self.rpc_client = rpc_client
        self.birdeye_api_key = birdeye_api_key
        self.logger = logging.getLogger(__name__)
        
        # Known rug patterns and blacklists
        self.known_rug_creators = set()
        self.suspicious_patterns = []
        self.load_blacklists()
    
    def load_blacklists(self):
        """Load known rug creators and patterns"""
        try:
            if os.path.exists('rug_blacklist.json'):
                with open('rug_blacklist.json', 'r') as f:
                    data = json.load(f)
                    self.known_rug_creators = set(data.get('creators', []))
                    self.suspicious_patterns = data.get('patterns', [])
        except Exception as e:
            self.logger.warning(f"Could not load blacklist: {e}")
    
    async def analyze_token_safety(self, token_address: str) -> TokenSafety:
        """
        Comprehensive token safety analysis
        
        Returns TokenSafety object with detailed analysis
        """
        try:
            token_pubkey = Pubkey.from_string(token_address)
            warnings = []
            checks = {}
            risk_score = 0
            metadata = {}
            
            # 1. Check mint authority
            mint_disabled = await self._check_mint_authority(token_pubkey)
            checks['mint_authority_disabled'] = mint_disabled
            if not mint_disabled:
                warnings.append("⚠️ Mint authority not disabled - tokens can be minted")
                risk_score += 30
            
            # 2. Check freeze authority
            freeze_disabled = await self._check_freeze_authority(token_pubkey)
            checks['freeze_authority_disabled'] = freeze_disabled
            if not freeze_disabled:
                warnings.append("⚠️ Freeze authority not disabled - tokens can be frozen")
                risk_score += 25
            
            # 3. Check token metadata
            token_metadata = await self._get_token_metadata(token_pubkey)
            metadata.update(token_metadata)
            
            # 4. Check creator wallet
            creator_risk = await self._analyze_creator_wallet(token_metadata.get('creator'))
            checks['creator_safe'] = creator_risk < 50
            if creator_risk > 50:
                warnings.append(f"⚠️ Creator wallet shows suspicious patterns (risk: {creator_risk}%)")
                risk_score += creator_risk // 2
            
            # 5. Check liquidity
            liquidity_info = await self._check_liquidity(token_address)
            checks['sufficient_liquidity'] = liquidity_info['sufficient']
            checks['liquidity_locked'] = liquidity_info['locked']
            metadata['liquidity'] = liquidity_info
            
            if not liquidity_info['sufficient']:
                warnings.append("⚠️ Insufficient liquidity for safe trading")
                risk_score += 20
            
            if not liquidity_info['locked']:
                warnings.append("⚠️ Liquidity not locked - can be removed")
                risk_score += 15
            
            # 6. Check token distribution
            distribution = await self._analyze_token_distribution(token_pubkey)
            checks['healthy_distribution'] = distribution['healthy']
            metadata['distribution'] = distribution
            
            if not distribution['healthy']:
                warnings.append("⚠️ Unhealthy token distribution - high concentration")
                risk_score += 10
            
            # 7. Check social signals
            social_score = await self._check_social_signals(token_metadata)
            checks['social_verified'] = social_score > 70
            metadata['social_score'] = social_score
            
            if social_score < 30:
                warnings.append("⚠️ Weak social presence - potential scam")
                risk_score += 15
            
            # 8. Check for known patterns
            pattern_risk = self._check_known_patterns(token_metadata, distribution)
            if pattern_risk > 0:
                warnings.append(f"⚠️ Matches known rug patterns (risk: {pattern_risk}%)")
                risk_score += pattern_risk
            
            # Determine overall safety
            is_safe = risk_score < 50 and mint_disabled and freeze_disabled
            
            return TokenSafety(
                token_address=token_address,
                symbol=token_metadata.get('symbol', 'UNKNOWN'),
                is_safe=is_safe,
                risk_score=min(risk_score, 100),
                warnings=warnings,
                checks=checks,
                metadata=metadata
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing token safety: {e}")
            return TokenSafety(
                token_address=token_address,
                symbol="ERROR",
                is_safe=False,
                risk_score=100,
                warnings=[f"Analysis failed: {str(e)}"],
                checks={},
                metadata={}
            )
    
    async def _check_mint_authority(self, token_pubkey: Pubkey) -> bool:
        """Check if mint authority is disabled"""
        try:
            account_info = await self.rpc_client.get_account_info(token_pubkey)
            if account_info.value is None:
                return False
            
            # Parse mint account data
            data = account_info.value.data
            if len(data) < 82:
                return False
            
            # Mint authority is at bytes 4-36 (32 bytes)
            # If all zeros, authority is disabled
            mint_authority = data[4:36]
            return all(b == 0 for b in mint_authority)
            
        except Exception as e:
            self.logger.error(f"Error checking mint authority: {e}")
            return False
    
    async def _check_freeze_authority(self, token_pubkey: Pubkey) -> bool:
        """Check if freeze authority is disabled"""
        try:
            account_info = await self.rpc_client.get_account_info(token_pubkey)
            if account_info.value is None:
                return False
            
            # Parse mint account data
            data = account_info.value.data
            if len(data) < 82:
                return False
            
            # Check if freeze authority exists (byte 46)
            has_freeze_authority = data[46] == 1
            if not has_freeze_authority:
                return True  # No freeze authority = safe
            
            # Freeze authority is at bytes 47-79 (32 bytes)
            freeze_authority = data[47:79]
            return all(b == 0 for b in freeze_authority)
            
        except Exception as e:
            self.logger.error(f"Error checking freeze authority: {e}")
            return False
    
    async def _get_token_metadata(self, token_pubkey: Pubkey) -> Dict[str, Any]:
        """Get token metadata from various sources"""
        metadata = {
            'symbol': 'UNKNOWN',
            'name': 'Unknown Token',
            'creator': None,
            'creation_time': None
        }
        
        try:
            # Try to get metadata from Birdeye
            if self.birdeye_api_key:
                birdeye_data = await self._get_birdeye_metadata(str(token_pubkey))
                if birdeye_data:
                    metadata.update(birdeye_data)
            
            # Add more metadata sources here
            
        except Exception as e:
            self.logger.error(f"Error getting token metadata: {e}")
        
        return metadata
    
    async def _get_birdeye_metadata(self, token_address: str) -> Optional[Dict]:
        """Get token metadata from Birdeye API"""
        try:
            url = f"https://public-api.birdeye.so/defi/token_overview"
            headers = {
                'X-API-KEY': self.birdeye_api_key,
                'Content-Type': 'application/json'
            }
            params = {'address': token_address}
            
            response = requests.get(url, headers=headers, params=params, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('data'):
                    token_data = data['data']
                    return {
                        'symbol': token_data.get('symbol', 'UNKNOWN'),
                        'name': token_data.get('name', 'Unknown'),
                        'price': token_data.get('price', 0),
                        'market_cap': token_data.get('mc', 0),
                        'liquidity': token_data.get('liquidity', 0),
                        'volume_24h': token_data.get('v24hUSD', 0)
                    }
        except Exception as e:
            self.logger.error(f"Error getting Birdeye metadata: {e}")
        
        return None
    
    async def _analyze_creator_wallet(self, creator_address: Optional[str]) -> int:
        """Analyze creator wallet for suspicious patterns"""
        if not creator_address:
            return 50  # Unknown creator = medium risk
        
        risk_score = 0
        
        try:
            # Check if creator is in known rug list
            if creator_address in self.known_rug_creators:
                return 100  # Maximum risk
            
            # Check creator wallet age and activity
            creator_pubkey = PublicKey(creator_address)
            
            # Add more creator analysis here
            # - Wallet age
            # - Previous token creations
            # - Transaction patterns
            # - Associated addresses
            
        except Exception as e:
            self.logger.error(f"Error analyzing creator wallet: {e}")
            risk_score = 30  # Default risk for analysis failure
        
        return risk_score
    
    async def _check_liquidity(self, token_address: str) -> Dict[str, Any]:
        """Check token liquidity status"""
        liquidity_info = {
            'sufficient': False,
            'locked': False,
            'amount_usd': 0,
            'pools': []
        }
        
        try:
            # Check liquidity via Birdeye or other sources
            if self.birdeye_api_key:
                metadata = await self._get_birdeye_metadata(token_address)
                if metadata:
                    liquidity_usd = metadata.get('liquidity', 0)
                    liquidity_info['amount_usd'] = liquidity_usd
                    liquidity_info['sufficient'] = liquidity_usd > 5000  # $5K minimum
            
            # TODO: Check if liquidity is locked
            # This requires checking LP token burns or lock contracts
            
        except Exception as e:
            self.logger.error(f"Error checking liquidity: {e}")
        
        return liquidity_info
    
    async def _analyze_token_distribution(self, token_pubkey: Pubkey) -> Dict[str, Any]:
        """Analyze token holder distribution"""
        distribution = {
            'healthy': True,
            'top_holders_percentage': 0,
            'holder_count': 0,
            'concentration_risk': 'low'
        }
        
        try:
            # Get largest token accounts
            largest_accounts = await self.rpc_client.get_token_largest_accounts(token_pubkey)
            
            if largest_accounts.value:
                total_supply = sum(account.amount for account in largest_accounts.value)
                if total_supply > 0:
                    # Calculate top 10 holders percentage
                    top_10_amount = sum(account.amount for account in largest_accounts.value[:10])
                    top_percentage = (top_10_amount / total_supply) * 100
                    
                    distribution['top_holders_percentage'] = top_percentage
                    distribution['holder_count'] = len(largest_accounts.value)
                    
                    # Determine concentration risk
                    if top_percentage > 80:
                        distribution['concentration_risk'] = 'high'
                        distribution['healthy'] = False
                    elif top_percentage > 60:
                        distribution['concentration_risk'] = 'medium'
                    else:
                        distribution['concentration_risk'] = 'low'
        
        except Exception as e:
            self.logger.error(f"Error analyzing token distribution: {e}")
            distribution['healthy'] = False
        
        return distribution
    
    async def _check_social_signals(self, metadata: Dict[str, Any]) -> int:
        """Check social media presence and signals"""
        social_score = 50  # Default neutral score
        
        try:
            # Check for social media links in metadata
            # Check Twitter/X presence
            # Check Telegram groups
            # Check website
            # Check community activity
            
            # This is a placeholder - implement actual social signal checking
            symbol = metadata.get('symbol', '')
            if len(symbol) > 2 and symbol.isalpha():
                social_score += 10  # Has proper symbol
            
            if metadata.get('name') and len(metadata['name']) > 3:
                social_score += 10  # Has proper name
            
        except Exception as e:
            self.logger.error(f"Error checking social signals: {e}")
        
        return min(social_score, 100)
    
    def _check_known_patterns(self, metadata: Dict[str, Any], distribution: Dict[str, Any]) -> int:
        """Check for known rug pull patterns"""
        pattern_risk = 0
        
        try:
            # Check for suspicious naming patterns
            symbol = metadata.get('symbol', '').lower()
            name = metadata.get('name', '').lower()
            
            # Common rug patterns
            suspicious_words = ['moon', 'safe', 'doge', 'shib', 'elon', 'pump', 'gem']
            if any(word in symbol or word in name for word in suspicious_words):
                pattern_risk += 5
            
            # Check for high concentration
            if distribution.get('concentration_risk') == 'high':
                pattern_risk += 15
            
            # Check for very new tokens with high market cap
            market_cap = metadata.get('market_cap', 0)
            if market_cap > 1000000:  # $1M+ market cap
                pattern_risk += 10  # Suspicious for new tokens
            
        except Exception as e:
            self.logger.error(f"Error checking known patterns: {e}")
        
        return pattern_risk
    
    def add_to_blacklist(self, creator_address: str, reason: str):
        """Add creator to blacklist"""
        self.known_rug_creators.add(creator_address)
        self.logger.warning(f"Added to blacklist: {creator_address} - {reason}")
        
        # Save updated blacklist
        try:
            blacklist_data = {
                'creators': list(self.known_rug_creators),
                'patterns': self.suspicious_patterns,
                'updated': datetime.now().isoformat()
            }
            with open('rug_blacklist.json', 'w') as f:
                json.dump(blacklist_data, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving blacklist: {e}")
