import statistics
from solders.pubkey import Pubkey
from ...core.client import SolanaClient
from .plugin import PriorityFeePlugin
from ...utils.logger import get_logger

logger = get_logger(__name__)

class DynamicPriorityFee(PriorityFeePlugin):
    """Dynamic priority fee plugin using getRecentPrioritizationFees."""
    def __init__(self, client: SolanaClient):
        self.client = client
    async def get_priority_fee(self, accounts: list[Pubkey] | None = None) -> int | None:
        try:
            body = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getRecentPrioritizationFees",
                "params": [[str(account) for account in accounts]] if accounts else [],
            }
            response = await self.client.post_rpc(body)
            if not response or "result" not in response:
                logger.error("Failed to fetch recent prioritization fees: invalid response")
                return None
            fees = [fee["prioritizationFee"] for fee in response["result"]]
            if not fees:
                logger.warning("No prioritization fees found in the response")
                return None
            prior_fee = int(statistics.quantiles(fees, n=10)[-3])  # 70th percentile
            return prior_fee
        except Exception as e:
            logger.error(f"Failed to fetch recent priority fee: {str(e)}", exc_info=True)
            return None
