Metadata-Version: 2.4
Name: advanced-trading-system
Version: 0.1.0
Summary: A bot for trading on Solana.
Requires-Python: >=3.9
Requires-Dist: solana>=0.32.0
Requires-Dist: solders>=0.19.0
Requires-Dist: anchorpy>=0.20.1
Requires-Dist: jito-searcher-client@ git+https://github.com/jito-labs/jito-python.git#subdirectory=jito_searcher_client
Requires-Dist: python-dotenv>=1.0.1
Requires-Dist: pyyaml>=6.0.1
Requires-Dist: loguru>=0.7.2
Requires-Dist: requests>=2.31.0
Requires-Dist: aiohttp>=3.8.3
Requires-Dist: apscheduler==3.10.1
Requires-Dist: tabulate
Requires-Dist: numpy
Requires-Dist: pandas
Requires-Dist: sqlalchemy>=1.4.0
Requires-Dist: redis>=4.0.0
Requires-Dist: httpx>=0.24.0
Requires-Dist: pydantic>=2.0.0
Provides-Extra: dev
Requires-Dist: pytest>=7.2.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: flake8>=6.0.0; extra == "dev"
Requires-Dist: ruff>=0.10.0; extra == "dev"
