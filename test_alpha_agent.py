#!/usr/bin/env python3
"""
Test script for ALPHA Agent signal generation.
Verifies that the ALPHA Agent can connect to data sources and generate momentum-based trading signals.
"""

import asyncio
import logging
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), 'src')))

# Test basic imports first
try:
    from src.data.models import SignalModel
    print("✅ SignalModel import successful")
except ImportError as e:
    print(f"❌ SignalModel import failed: {e}")
    # Create a simple SignalModel for testing
    from datetime import datetime
    from typing import Optional

    class SignalModel:
        def __init__(self, token: str, entry_price: float, direction: str,
                     confidence: float, timestamp: Optional[datetime] = None,
                     source: Optional[str] = None):
            self.token = token
            self.entry_price = entry_price
            self.direction = direction
            self.confidence = confidence
            self.timestamp = timestamp or datetime.now()
            self.source = source

try:
    from src.agents.alpha_agent import AlphaAgent
    print("✅ AlphaAgent import successful")
except ImportError as e:
    print(f"❌ AlphaAgent import failed: {e}")
    AlphaAgent = None

try:
    from src.data.phase1_data_pipeline_unification import UnifiedDataService
    print("✅ UnifiedDataService import successful")
except ImportError as e:
    print(f"❌ UnifiedDataService import failed: {e}")
    UnifiedDataService = None

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_alpha_agent_initialization():
    """Test ALPHA Agent initialization"""
    logger.info("🧪 Testing ALPHA Agent initialization...")

    if AlphaAgent is None:
        logger.error("❌ AlphaAgent not available - skipping test")
        return None

    try:
        # Initialize data service
        data_service = UnifiedDataService()
        
        # Initialize ALPHA Agent
        alpha_agent = AlphaAgent(
            data_service=data_service,
            enable_ml=False  # Disable ML for basic test
        )
        
        # Initialize the agent
        await alpha_agent.initialize()
        
        # Check health
        health_status = alpha_agent.health()
        system_status = alpha_agent.get_system_status()
        
        logger.info(f"✅ ALPHA Agent initialized successfully")
        logger.info(f"📊 Health Status: {health_status}")
        logger.info(f"🔧 System Status: {system_status}")
        
        return alpha_agent
        
    except Exception as e:
        logger.error(f"❌ ALPHA Agent initialization failed: {e}")
        return None


async def test_data_connection():
    """Test data service connection"""
    logger.info("🧪 Testing data service connection...")

    if UnifiedDataService is None:
        logger.error("❌ UnifiedDataService not available - skipping test")
        return False

    try:
        data_service = UnifiedDataService()
        
        # Test SOL price fetch
        sol_mint = "So11111111111111111111111111111111111111112"
        price_data = await data_service.get_price(sol_mint)
        
        if price_data:
            logger.info(f"✅ Data connection successful")
            logger.info(f"💰 SOL Price: ${price_data.price_usd:.2f}")
            volume_str = f"${price_data.volume_24h:,.2f}" if price_data.volume_24h is not None else "N/A"
            logger.info(f"📈 Volume 24h: {volume_str}")
            logger.info(f"🔄 Source: {price_data.source}")
            return True
        else:
            logger.warning("⚠️ No price data received")
            return False
            
    except Exception as e:
        logger.error(f"❌ Data connection test failed: {e}")
        return False


async def test_signal_generation():
    """Test ALPHA Agent signal generation"""
    logger.info("🧪 Testing ALPHA Agent signal generation...")
    
    try:
        # Initialize ALPHA Agent
        alpha_agent = await test_alpha_agent_initialization()
        if not alpha_agent:
            return False
            
        # Test signal generation for SOL
        sol_mint = "So11111111111111111111111111111111111111112"
        
        logger.info(f"🎯 Generating signal for SOL ({sol_mint[:8]}...)")
        signal = await alpha_agent.process(sol_mint)
        
        if signal:
            logger.info(f"✅ Signal generated successfully!")
            logger.info(f"🪙 Token: {signal.token[:8]}...")
            logger.info(f"💵 Entry Price: ${signal.entry_price:.2f}")
            logger.info(f"📊 Direction: {signal.direction}")
            logger.info(f"🎯 Confidence: {signal.confidence:.2f}")
            logger.info(f"⏰ Timestamp: {signal.timestamp}")
            logger.info(f"🔍 Source: {signal.source}")
            return True
        else:
            logger.info("ℹ️ No signal generated (market conditions may not meet criteria)")
            return True  # This is still a successful test
            
    except Exception as e:
        logger.error(f"❌ Signal generation test failed: {e}")
        return False


async def test_momentum_logic():
    """Test momentum signal logic with simulated data"""
    logger.info("🧪 Testing momentum signal logic...")
    
    try:
        alpha_agent = await test_alpha_agent_initialization()
        if not alpha_agent:
            return False
            
        # Test with simulated market data
        test_cases = [
            {
                'name': 'High Price SOL (should trigger BUY)',
                'market_data': {
                    'token_address': 'So11111111111111111111111111111111111111112',
                    'price_usd': 200.0,  # High price to trigger heuristic
                    'volume_24h': 1000000,
                    'source': 'test'
                }
            },
            {
                'name': 'Low Price SOL (should not trigger)',
                'market_data': {
                    'token_address': 'So11111111111111111111111111111111111111112',
                    'price_usd': 50.0,  # Low price
                    'volume_24h': 1000000,
                    'source': 'test'
                }
            }
        ]
        
        for test_case in test_cases:
            logger.info(f"🔬 Testing: {test_case['name']}")
            
            # Generate signal with test data
            signal = await alpha_agent.generate_signal(
                test_case['market_data'], 
                test_case['market_data']['token_address']
            )
            
            if signal:
                logger.info(f"  ✅ Signal: {signal.direction} @ ${signal.entry_price:.2f} (confidence: {signal.confidence:.2f})")
            else:
                logger.info(f"  ℹ️ No signal generated")
                
        return True
        
    except Exception as e:
        logger.error(f"❌ Momentum logic test failed: {e}")
        return False


async def main():
    """Main test function"""
    logger.info("🚀 Starting ALPHA Agent Test Suite")
    logger.info("=" * 60)
    
    test_results = []
    
    # Test 1: Data Connection
    logger.info("\n📡 TEST 1: Data Service Connection")
    result1 = await test_data_connection()
    test_results.append(("Data Connection", result1))
    
    # Test 2: Agent Initialization
    logger.info("\n🤖 TEST 2: ALPHA Agent Initialization")
    agent = await test_alpha_agent_initialization()
    result2 = agent is not None
    test_results.append(("Agent Initialization", result2))
    
    # Test 3: Signal Generation
    logger.info("\n🎯 TEST 3: Signal Generation")
    result3 = await test_signal_generation()
    test_results.append(("Signal Generation", result3))
    
    # Test 4: Momentum Logic
    logger.info("\n📈 TEST 4: Momentum Logic")
    result4 = await test_momentum_logic()
    test_results.append(("Momentum Logic", result4))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📋 TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name:.<30} {status}")
        if result:
            passed += 1
    
    logger.info("-" * 60)
    logger.info(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED! ALPHA Agent is ready for deployment.")
        return True
    else:
        logger.error(f"⚠️ {total - passed} tests failed. Please review the issues above.")
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}")
        sys.exit(1)
